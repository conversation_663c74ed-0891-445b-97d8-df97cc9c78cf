'use client'

import { Equipment } from '@/types/equipment'

interface EquipmentCardProps {
  equipment: Equipment
  onClick: () => void
}

export default function EquipmentCard({ equipment, onClick }: EquipmentCardProps) {
  return (
    <div 
      className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer overflow-hidden"
      onClick={onClick}
    >
      {/* Equipment Image */}
      <div className="h-48 bg-gray-200 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary-dark/20 flex items-center justify-center">
          <span className="text-6xl opacity-50">📷</span>
        </div>
        {equipment.availability && (
          <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            Available
          </div>
        )}
      </div>

      {/* Equipment Info */}
      <div className="p-4">
        <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-2">
          {equipment.name}
        </h3>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {equipment.shortDescription}
        </p>

        {/* Rating */}
        <div className="flex items-center mb-3">
          <div className="flex text-yellow-400">
            {[...Array(5)].map((_, i) => (
              <span key={i} className={i < Math.floor(equipment.rating) ? 'text-yellow-400' : 'text-gray-300'}>
                ★
              </span>
            ))}
          </div>
          <span className="text-sm text-gray-500 ml-2">
            ({equipment.reviewCount})
          </span>
        </div>

        {/* Pricing */}
        <div className="border-t pt-3">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-2xl font-bold text-primary">
                ${equipment.price.daily}
              </span>
              <span className="text-gray-500 text-sm">/day</span>
            </div>
            <button className="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors">
              View Details
            </button>
          </div>
          
          <div className="text-xs text-gray-500 mt-1">
            Weekly: ${equipment.price.weekly} • Monthly: ${equipment.price.monthly}
          </div>
        </div>
      </div>
    </div>
  )
}
