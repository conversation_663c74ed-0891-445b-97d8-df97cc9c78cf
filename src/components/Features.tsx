export default function Features() {
  const features = [
    {
      icon: '🚚',
      title: 'FREE DELIVERY',
      description: 'Free delivery and pickup within Melbourne metro area for orders over $200'
    },
    {
      icon: '⚡',
      title: 'QUICK SETUP',
      description: 'Professional setup service available. Get your equipment ready to use immediately'
    },
    {
      icon: '🛡️',
      title: 'FULLY INSURED',
      description: 'All equipment is fully insured and maintained to the highest professional standards'
    }
  ]

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
