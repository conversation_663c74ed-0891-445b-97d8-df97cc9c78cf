'use client'

import { useState } from 'react'
import { equipmentData } from '@/data/equipment'
import { Equipment, EquipmentCategory } from '@/types/equipment'
import EquipmentCard from './EquipmentCard'
import EquipmentModal from './EquipmentModal'

export default function EquipmentGrid() {
  const [selectedCategory, setSelectedCategory] = useState<EquipmentCategory | 'all'>('all')
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null)

  const categories = [
    { key: 'all', label: 'All Equipment' },
    { key: EquipmentCategory.PROJECTOR, label: 'Projectors' },
    { key: EquipmentCategory.SCREEN, label: 'Screens' },
    { key: EquipmentCategory.AUDIO, label: 'Audio' },
    { key: EquipmentCategory.LIGHTING, label: 'Lighting' },
    { key: EquipmentCategory.CAMERA_STABILIZER, label: 'Stabilizers' },
    { key: EquipmentCategory.TRIPOD, label: 'Tripods' }
  ]

  const filteredEquipment = selectedCategory === 'all' 
    ? equipmentData 
    : equipmentData.filter(item => item.category === selectedCategory)

  const handleEquipmentClick = (equipment: Equipment) => {
    setSelectedEquipment(equipment)
  }

  const closeModal = () => {
    setSelectedEquipment(null)
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Equipment
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Professional-grade equipment for all your event and production needs
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category) => (
            <button
              key={category.key}
              onClick={() => setSelectedCategory(category.key as EquipmentCategory | 'all')}
              className={`px-6 py-3 rounded-full font-medium transition-colors ${
                selectedCategory === category.key
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>

        {/* Equipment Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredEquipment.map((equipment) => (
            <EquipmentCard
              key={equipment.id}
              equipment={equipment}
              onClick={() => handleEquipmentClick(equipment)}
            />
          ))}
        </div>

        {/* Equipment Modal */}
        {selectedEquipment && (
          <EquipmentModal
            equipment={selectedEquipment}
            isOpen={!!selectedEquipment}
            onClose={closeModal}
          />
        )}
      </div>
    </section>
  )
}
