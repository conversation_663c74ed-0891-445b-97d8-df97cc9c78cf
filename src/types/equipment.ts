export interface Equipment {
  id: string;
  name: string;
  category: EquipmentCategory;
  description: string;
  shortDescription: string;
  price: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  image: string;
  features: string[];
  specifications: Record<string, string>;
  availability: boolean;
  rating: number;
  reviewCount: number;
}

export enum EquipmentCategory {
  PROJECTOR = 'projector',
  SCREEN = 'screen',
  AUDIO = 'audio',
  LIGHTING = 'lighting',
  CAMERA_STABILIZER = 'camera-stabilizer',
  TRIPOD = 'tripod'
}

export interface RentalRequest {
  equipmentId: string;
  startDate: Date;
  endDate: Date;
  quantity: number;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
  totalPrice: number;
  duration: number;
  durationType: 'daily' | 'weekly' | 'monthly';
}
