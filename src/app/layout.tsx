import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'EquipRent Melbourne - Equipment Rental Services',
  description: 'Professional equipment rental services in Melbourne. Rent projectors, screens, audio equipment, lighting, camera stabilizers, and tripods for your events.',
  keywords: 'equipment rental, Melbourne, projector rental, audio equipment, lighting rental, camera equipment',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
