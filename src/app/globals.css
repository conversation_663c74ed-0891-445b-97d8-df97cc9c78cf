@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #E94E22;
  --primary-dark: #d63e1a;
  --primary-light: #f05e32;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', sans-serif;
}

body {
  color: #333;
  background: #fff;
  line-height: 1.6;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom utility classes */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus styles */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Animation classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Responsive text sizes */
@media (max-width: 640px) {
  .text-responsive-xl {
    font-size: 1.5rem;
  }
}

@media (min-width: 641px) {
  .text-responsive-xl {
    font-size: 2rem;
  }
}

@media (min-width: 1024px) {
  .text-responsive-xl {
    font-size: 2.5rem;
  }
}
